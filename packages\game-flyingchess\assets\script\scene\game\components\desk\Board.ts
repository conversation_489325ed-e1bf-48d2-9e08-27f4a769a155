import {
    _decorator,
    Component,
    Node,
    Vec3,
    UITransform,
    error,
    instantiate,
    Prefab,
    tween,
    v3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import store from '@/core/business/store'
import {
    GameModelSinglePosMap,
    HeadNodeDefaultPosMap,
    Single,
} from '@/core/business/types/IGame'
import { sleep } from '@/core/business/util/TimeUtils'
import { PlayerItem, PlayerState } from '../player/PlayerItem'

const { ccclass, property } = _decorator

// 玩家位置枚举
export enum PlayerPosition {
    BLUE = 0, // 蓝色玩家
    RED = 1, // 红色玩家
    GREEN = 2, // 绿色玩家
    YELLOW = 3, // 黄色玩家
}

// 玩家旋转角度映射（按照你的要求）
// 蓝色：顺时针旋转180度
// 红色：顺时针旋转90度
// 绿色：不旋转
// 黄色：逆时针旋转90度
const PLAYER_ROTATION_ANGLES = [180, -90, 0, 90] as const

@ccclass('Board')
export class Board extends BaseComponent {
    @property({ type: Node, tooltip: '玩家根节点' })
    playerRootNode: Node = null!

    @property({ type: Prefab, tooltip: '玩家预制体' })
    players_prefab: Prefab = null!

    gamePlayers: Node[] = []

    private boardSize: number = 0
    private gridSize: number = 0

    protected override onLoad(): void {}

    protected override initUI(): void {
        window.ccLog('棋盘初始化...')
        this.initBorad()
    }

    private initBorad() {
        const currentPlayerPositon = store.game.currentPlayerPositon

        // 根据玩家位置设置棋盘旋转角度
        const rotationAngle = PLAYER_ROTATION_ANGLES[currentPlayerPositon]
        window.ccLog('当前玩家', store.game.currentPlayer)
        window.ccLog('当前玩家位置', currentPlayerPositon)
        window.ccLog('当前玩家旋转角度', rotationAngle)
        // 设置棋盘旋转
        this.node.setRotationFromEuler(new Vec3(0, 0, rotationAngle))
    }

    /**
     * 根据当前玩家位置计算显示位置索引
     * 当前玩家始终显示在左下角(位置0)，其他玩家按position顺时针排列
     */
    private getDisplayPositionIndex(playerPosition: number): number {
        const currentPlayerPosition = store.game.currentPlayerPositon
        // 计算相对位置，确保当前玩家在位置0（左下角）
        return (playerPosition - currentPlayerPosition + 4) % 4
    }

    async initPlayersDynamic() {
        window.ccLog('Board-->initPlayersDynamic')
        this.playerRootNode.removeAllChildren()
        this.gamePlayers = []

        const copy = [...store.game.playerList]
        if (!copy.length) return error('玩家列表不存在')

        // 先按 GameModelSinglePosMap 的位置排列玩家
        const playersPos = GameModelSinglePosMap[copy.length as Single]
        const newPlayerNodes: Node[] = []

        store.game.playerList.forEach((player, index) => {
            const player_node = instantiate(this.players_prefab)
            const { x, y } = playersPos[index]

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(x, y, 0))
                .addToParent(this.playerRootNode, {
                    props: { player },
                    data: { state: PlayerState.NORMAL },
                })

            newPlayerNodes.push(player_node)
        })

        // 等待 1 秒
        await sleep(1000)

        // 头像飞到各自的最终位置，根据当前玩家位置重新计算显示位置
        const headTweenPromises: Promise<void>[] = []

        newPlayerNodes.forEach((node, index) => {
            const player = store.game.playerList[index]
            // 计算显示位置索引，当前玩家显示在左下角
            const displayIndex = this.getDisplayPositionIndex(player.position)
            const finalPos = HeadNodeDefaultPosMap[4][displayIndex]

            headTweenPromises.push(
                new Promise((resolve) => {
                    tween(node)
                        .to(0.8, {
                            position: v3(finalPos.x, finalPos.y, 0),
                        })
                        .delay(0.2 * index)
                        .call(() => {
                            resolve()
                        })
                        .start()
                })
            )
        })

        await Promise.all(headTweenPromises)

        // 将节点添加到 gamePlayers 数组中
        this.gamePlayers = newPlayerNodes
    }

    // 游戏已经启动，直接显示玩家头像
    initPlayerStatic() {
        window.ccLog('Board-->initPlayerStatic')
        this.playerRootNode.removeAllChildren()
        this.gamePlayers = []

        const copy = [...store.game.playerList]
        if (!copy.length) return error('玩家列表不存在')

        // 直接把玩家头像显示在重新计算后的位置上
        store.game.playerList.forEach((player) => {
            const player_node = instantiate(this.players_prefab)
            // 计算显示位置索引，当前玩家显示在左下角
            const displayIndex = this.getDisplayPositionIndex(player.position)
            const finalPos = HeadNodeDefaultPosMap[4][displayIndex]

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(finalPos.x, finalPos.y, 0))
                .addToParent(this.playerRootNode, {
                    props: { player },
                    data: { state: PlayerState.NORMAL },
                })

            this.gamePlayers.push(player_node)
        })
    }

    override onDestroy(): void {}
}
