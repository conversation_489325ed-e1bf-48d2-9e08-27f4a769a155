import {
    _decorator,
    Component,
    error,
    ImageAsset,
    Input,
    instantiate,
    isValid,
    Label,
    log,
    math,
    Node,
    Prefab,
    sp,
    Sprite,
    Sprite<PERSON>rame,
    Tween,
    tween,
    v2,
    v3,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import {
    AudioEffectConstant,
    GameEventConstant,
    RoomEventConstant,
} from '@/core/business/constant'
import { SocketEvent } from '@/core/business/ws'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import {
    State as DeskState,
    State,
} from '@/pb-generate/server/dixit/v1/state_pb'
import { Player, PlayerSchema } from '@/pb-generate/server/dixit/v1/player_pb'
import {
    GameOverBroadcast,
    GameInfoBroadcast,
} from '@/pb-generate/server/dixit/v1/game_pb'

// import { ThrowMine } from './ThrowMine'
// import { Curse } from './Curse'
import {
    BattleMode,
    Player as BasePlayer,
    PlayerSchema as BasePlayerSchema,
} from 'sgc'
import { PlayerType } from '@/core/business/store/game'
import { Dress } from '@/core/components/Dress'

const { ccclass, property } = _decorator

export enum ShowType {
    /**正常 */
    NORMAL = 0,
    /**计分榜 */
    SCORE_BOARD,
}

export type PlayerItemProps = {
    player: PlayerType
}

export type PlayerItemData = {
    state?: PlayerState
    basePlayer?: BasePlayer
    showType?: ShowType
    isSpeaker?: boolean
    isDressInit?: boolean
}

/**玩家状态 */
export enum PlayerState {
    /**正常 */
    NORMAL = 0,
    /**托管 */
    AUTO,
    /**离开 */
    LEAVE,
    /**观战 */
    WATCH,
    /*空位置*/
    EMPTY,
}

@ccclass('PlayerItem')
export class PlayerItem extends BaseComponent<PlayerItemProps, PlayerItemData> {
    @property({ type: Label, tooltip: '昵称节点' })
    nick_name_node: Label = null!

    @property({ type: Sprite, tooltip: '头像节点' })
    avatar_node: Sprite = null!

    @property({ type: Node, tooltip: '标识节点' })
    tag_node: Node = null!

    @property({ type: Node, tooltip: '托管标识节点' })
    auto_node: Node = null!

    @property({ type: Node, tooltip: '离开标识节点' })
    leave_node: Node = null!

    @property({ type: Node, tooltip: '观战标识节点' })
    watch_node: Node = null!

    @property({ type: Node, tooltip: '空位置节点' })
    empty_node: Node = null!

    @property(Prefab)
    dressPrefab: Prefab = null!

    @property(Node)
    dress_container: Node = null!

    private dress: Dress | null = null

    override props: PlayerItemProps = {
        player: { ...create(PlayerSchema), position: 0 },
    }

    override data: PlayerItemData = {
        state: PlayerState.NORMAL,
        basePlayer: create(BasePlayerSchema),
        showType: ShowType.NORMAL,
    }

    protected override onLoad(): void {
        this.node.on(Input.EventType.TOUCH_END, this.onClickPlayerHandler, this)
    }

    protected override start(): void {
        window.ccLog('PlayerItem-->start', this.props.player)
    }

    protected override initUI(): void {
        const { player } = this.props
        const { game } = store

        window.ccLog('PlayerItem-->initUI,player', player)

        const base = game.getBasePlayerByUid(player.id)
        base && (this.data.basePlayer = base)

        this.nick_name_node.string = `${cat.util.stringUtil.sub(
            base?.nickname ?? '',
            8
        )}`

        window.ccLog('PlayerItem-->initUI2,base', base)

        base?.avatar &&
            cat.res.loadRemote(
                base?.avatar,
                (err: Error, imageAsset: ImageAsset) => {
                    if (err) return error(err)
                    if (isValid(this.avatar_node)) {
                        const spriteFrame =
                            SpriteFrame.createWithImage(imageAsset)
                        spriteFrame.packable = false
                        this.avatar_node.spriteFrame = spriteFrame
                    }
                }
            )

        if (this.data.showType === ShowType.SCORE_BOARD) {
            //设置nick_name_node颜色0x62554a
            this.nick_name_node.color = new math.Color(
                0x62 / 255,
                0x55 / 255,
                0x4a / 255
            )
        }
    }

    public initDress() {
        // 初始化 Dress 组件
        if (this.dressPrefab && this.dress_container) {
            // 清空容器
            this.dress_container.destroyAllChildren()

            // 实例化 Dress 预制体
            const dressNode = instantiate(this.dressPrefab)
            this.dress_container.addChild(dressNode)

            // 获取 Dress 组件
            this.dress = dressNode.getComponent(Dress)

            // 设置属性
            if (this.dress) {
                window.ccLog(
                    'PlayerItem-->initDress,playerId:',
                    this.props.player.id
                )
                this.dress.setUpdateProps({
                    playerId: this.props.player.id,
                })
                setTimeout(() => {
                    cat.event.dispatchEvent(RoomEventConstant.UPDATE_PLATFORM)
                }, 1000)
                this.data.isDressInit = true
            }
        }
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                if (this.data.state === PlayerState.AUTO) {
                    //进入托管
                    window.ccLog('用户进入托管')
                    cat.tracking.game.gameEntrust('on')
                } else {
                    //解除托管
                    window.ccLog('用户解除托管')
                    cat.tracking.game.gameEntrust('off')
                }

                window.ccLog('更新状态', this.data.state)
                this.auto_node.active =
                    this.leave_node.active =
                    this.watch_node.active =
                    this.empty_node.active =
                        false

                this.nick_name_node.node.active = true

                cat.event.dispatchEvent(GameEventConstant.UPDATE_PLAYER_STATE)
                switch (this.data.state) {
                    case PlayerState.LEAVE:
                        this.leave_node.active = true
                        break
                    case PlayerState.WATCH:
                        this.watch_node.active = true
                        break
                    case PlayerState.AUTO:
                        this.auto_node.active = true
                        break
                    case PlayerState.EMPTY:
                        this.empty_node.active = true
                        this.nick_name_node.node.active = false
                        break

                    case PlayerState.NORMAL:

                    default:
                        break
                }
                this.tag_node.active =
                    this.leave_node.active ||
                    this.watch_node.active ||
                    this.auto_node.active ||
                    this.empty_node.active
            },

            () => {
                this.updatePalyerInfoByPlayer()
            },
            () => {
                console.log('PlayerItem-->更新玩家信息,名称，头像')
                this.nick_name_node.string = `${cat.util.stringUtil.sub(
                    this.data.basePlayer?.nickname ?? '',
                    8
                )}`

                this.data.basePlayer?.avatar &&
                    cat.res.loadRemote(
                        this.data.basePlayer?.avatar,
                        (err: Error, imageAsset: ImageAsset) => {
                            if (err) return error(err)
                            if (isValid(this.avatar_node)) {
                                const spriteFrame =
                                    SpriteFrame.createWithImage(imageAsset)
                                spriteFrame.packable = false
                                this.avatar_node.spriteFrame = spriteFrame
                            }
                        }
                    )
            },

            () => {
                const isCountingVoteState = [State.PLAYER_VOTE_COUNT].includes(
                    store.game.roomData.stateInfo?.state!
                )

                const isGiveUp =
                    isCountingVoteState &&
                    !!this.props.player.nickname &&
                    !this.props.player.voted &&
                    !this.data.isSpeaker
            },

            () => {
                if (this.props.player.id && !this.data.isDressInit) {
                    setTimeout(() => {
                        this.initDress()
                    }, 500)
                }
            },
        ])
    }

    /**点击玩家 */
    onClickPlayerHandler() {
        if (cat.event.has(GameEventConstant.SELECT_PLAYER)) {
            cat.event.dispatchEvent(
                GameEventConstant.SELECT_PLAYER,
                this.props.player
            )
        } else {
            cat.platform.openProfile({ userId: Number(this.props.player.id) })
        }
    }

    /**更新玩家信息 */
    private updatePalyerInfoByPlayer() {
        //window.ccLog('PlayerItem-->updatePalyerInfoByPlayer:', this.data)
        const player = this.props.player

        // window.ccLog(
        //     'PlayerItem-->updatePalyerInfoByPlayer， player:',
        //     player.exited,
        //     'this.data.basePlayer:',
        //     this.data.basePlayer?.online
        // )

        // 状态
        if (this.data.state === PlayerState.EMPTY) return

        if (player.exited || !this.data.basePlayer?.online) {
            this.data.state = PlayerState.LEAVE
        } else if (player.hosting) {
            this.data.state = PlayerState.AUTO
        } else {
            this.data.state = PlayerState.NORMAL
        }
    }

    override onDestroy(): void {
        this.unscheduleAllCallbacks()
    }
}
