import {
    __private,
    _decorator,
    AudioSource,
    Button,
    error,
    ForwardFlow,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    sp,
    SpriteFrame,
    Tween,
    tween,
    UITransform,
} from 'cc'

import SceneLayer from '@/core/manager/gui/scene/SceneLayer'
import { UIGame } from '../game/ui/UIGame'
import store from '@/core/business/store'
import { cat } from '@/core/manager'
import {
    EnumJSBridgeWebView,
    GameCloseType,
    JSBridgeClient,
    JSBridgeWebView,
} from '@/core/business/jsbridge/JSBridge'
import JoinBattle from '@/core/business/hooks/JoinBattle'
import { SocketEvent } from '@/core/business/ws'
import { BattleInitialData, Player as BasePlayer, BattleMode } from 'sgc'
import { AudioEffectConstant } from '@/core/business/constant/AudioEffectConstant'
import { create, fromBinary } from '@bufbuild/protobuf'
import {
    GameOverBroadcast,
    GameOverBroadcastSchema,
    SyncResponseSchema,
    SyncResponseJson,
    SyncResponse,
    GameInfoBroadcastSchema,
} from '@/pb-generate/server/dixit/v1/game_pb'
import { State as DeskState } from '@/pb-generate/server/dixit/v1/state_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import { GameEventConstant } from '@/core/business/constant/GameEventConstant'
import { GameVisiable } from '@/core/business/store/global'
import {
    Player,
    PlayerInfoMessage,
} from '@/pb-generate/server/dixit/v1/player_pb'
import EnterGame from '@/core/business/hooks/EnterGame'
import { GlobalEventConstant } from '@/core/business/constant/GlobalEventConstant'
import { RoomEventConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

export type GameData = {
    isGameStartCalled: boolean
}

@ccclass('Game')
export class Game extends SceneLayer {
    @property(UIGame)
    uiGame: UIGame = null!

    private needCleanSceneOnGameStart: boolean = false
    private needRestartGameOnEventShow: boolean = false
    private gameStartParams: string = ''
    override async start() {
        store.game.reset()

        cat.platform.inputMode(1)
        if (store.game.isParamsFromUrl) {
            window.ccLog('Game-->start: JoinBattle 0')
            this.joinBattle(this.isReload)
        } else if (!store.game.gameLoadFinished) {
            // 第一次加载
            window.ccLog('正在登录中')
            cat.gui.showLoading({ title: '正在登录中' })
            store.game.gameLoadFinished = true
            JSBridgeClient.gameLoadFinished()
        } else {
            // 区分切后台重连和重新开局
            window.ccLog('Game-->start: JoinBattle 1')
            this.isReload && this.joinBattle(this.isReload)
        }
    }

    override data: GameData = {
        isGameStartCalled: false,
    }

    private async joinBattle(isReload: boolean = false) {
        await JoinBattle(isReload)
    }

    protected override onLoad(): void {}

    protected override onAutoObserver(): void {
        this.addAutorun(() => {
            this.uiGame.watch_node.active =
                this.data.isGameStartCalled && store.user.isAudience
        })
    }

    override onDestroy(): void {
        cat.audio.stopMusic()
        cat.platform.inputMode(0)
        store.game.reset()
        this.data.isGameStartCalled = false
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'BattleInitialize',
                this.onBattleInitializeHandler,
                this
            )
            .on<SocketEvent>(
                'PlayerStatusChanged',
                this.onPlayerStatusChangedHandler,
                this
            )
            .on<SocketEvent>(
                'MT_GAME_OVER_BROADCAST',
                this.onEventFinishedHandler,
                this
            )
            .on(GlobalEventConstant.EVENT_SHOW, this.onShowHandler, this)
    }

    protected override addListener(): void {
        // 监听游戏开始
        JSBridgeWebView.on(
            EnumJSBridgeWebView.GAME_START,
            this.onGameStart,
            this
        )

        JSBridgeWebView.on(
            EnumJSBridgeWebView.MIKE_USERS_INFO,
            this.updataMikeUsersInfo,
            this
        )
    }

    protected override removeListener(): void {
        JSBridgeWebView.off(
            EnumJSBridgeWebView.GAME_START,
            this.onGameStart,
            this
        )

        JSBridgeWebView.off(
            EnumJSBridgeWebView.MIKE_USERS_INFO,
            this.updataMikeUsersInfo,
            this
        )
    }

    private onShowHandler() {
        if (this.needRestartGameOnEventShow) {
            this.onGameStart(this.gameStartParams)
            this.needRestartGameOnEventShow = false
            this.gameStartParams = ''
        }
    }

    private async onGameStart(data: string) {
        // 开始游戏时处于后台状态
        this.data.isGameStartCalled = true
        if (!store.global.isGameVisiable) {
            this.gameStartParams = data
            this.needRestartGameOnEventShow = true
            return
        }
        // 上一局结束时处于后台状态未清理场景
        if (this.needCleanSceneOnGameStart) {
            this.needCleanSceneOnGameStart = false
            await cat.gui.cleanScene()
        }
        window.ccLog('游戏开始 启动参数', data)
        try {
            window.ccLog('Game-->onGameStart: JoinBattle')
            cat.gui.showLoading({ title: '连接服务器' })
            // 开始游戏
            await EnterGame(data)
            store.game.gameLoadFinished = true
            this.joinBattle()
        } catch (err) {
            error('监听游戏开始err', err)
            let message = '未知错误...'
            if (err instanceof Error) {
                message = err.message
            }
            JSBridgeClient.closeGame(
                GameCloseType.JoinOverTime,
                JSON.stringify({
                    err,
                    message,
                    info: '游戏开始错误',
                })
            )
        }
    }

    /**战局初始化 */
    private async onBattleInitializeHandler(_data: BattleInitialData) {
        window.ccLog('Game-->onBattleInitializeHandler:', _data)
        cat.audio.playMusic(AudioEffectConstant.BGM)
        const { data, players, started, battle } = _data
        const syncResponse = fromBinary(SyncResponseSchema, data)
        const { gameInfo, playerInfo } = syncResponse
        window.ccLog('战局同步', syncResponse)
        window.ccLog('基础玩家', players)
        window.ccLog('战局初始化', started)
        window.ccLog('战局信息', battle)
        // 初始化
        const { game } = store

        store.game.basePlayers = players.map((player) => {
            if (store.game.isParamsFromUrl && !player.avatar) {
                return {
                    ...player,
                    avatar: 'https://res-cdn.suileyoo.com/avatar/MITRCA/MITA06.png',
                }
            }
            return player
        })

        store.game.roomData = gameInfo!
        window.ccLog('Game-->store.game.playerInfo:', playerInfo)
        store.game.playerInfo = playerInfo!

        battle && (store.game.battle = battle)

        if (started) {
            //已经开始
            // 同步界面
            await this.syncHandle(syncResponse)
        } else {
            cat.tracking.game.roomWait()
            cat.gui.showLoading({ title: '等待其他玩家加入...' })
        }

        JSBridgeClient.getMikeUsersInfo().then((res) => {
            console.log('平台用户装扮信息========', JSON.stringify(res))
            this.updataMikeUsersInfo(res)
        })
    }

    private onPlayerStatusChangedHandler(data: BasePlayer) {
        window.ccLog('Game-->onPlayerStatusChangedHandler:', data)
        const basePlayer = store.game.getBasePlayerByUid(data.id)
        if (basePlayer) {
            basePlayer.online = data.online
        }

        const player = store.game.getPlayerByUid(data.id)
        if (player) {
            player.hosting = data.hosting
        }
    }

    async syncHandle(res: SyncResponse): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            const { gameInfo, playerInfo } = res

            const { currentPlayerCursor } = gameInfo!.roundInfo!
            const player = store.game.currentPlayer
            const state = gameInfo?.stateInfo?.state

            if (state != DeskState.UNSPECIFIED) {
                //游戏处于未开始阶段
                if (state === DeskState.GAME_FINISHED) {
                    cat.gui.hideLoading().showLoading({ title: '游戏已结束' }) //显示loading调用结算
                } else {
                    if (state != DeskState.GAME_START) {
                        this.uiGame.syncGameInfo()
                    }
                    // 同步桌子状态
                    cat.event.dispatchEvent<SocketEvent>(
                        'MT_GAME_INFO_BROADCAST',
                        gameInfo
                    )
                    // 同步桌子状态
                    cat.event.dispatchEvent<SocketEvent>(
                        'MT_PLAYER_INFO_MESSAGE',
                        playerInfo
                    )

                    cat.gui.hideLoading()
                }

                resolve()
            } else {
                //游戏处于带开局阶段
                reject()
            }
        })
    }

    private async onEventFinishedHandler() {
        await sleep(3000)
        window.ccLog('对局结束： onEventFinishedHandler')
        cat.event
            .dispatchEvent(GameEventConstant.CLOSE_COMMON_UI)
            .dispatchEvent(GameEventConstant.UPDATE_PLAYER_STATE)
        // cat.audio.playEffect(AudioEffectConstant.GAME_OVER)

        cat.gui.showLoading({ title: '结算中...' }) //显示loading调用结算

        if (store.global.isChannelWeb) {
            window.history.back()
            return
        }
        cat.ws.destroy()

        if (store.global.isGameVisiable) {
            store.reset()
            store.game.gameLoadFinished = true
            await cat.gui.cleanScene()
        } else {
            store.reset()
            store.game.gameLoadFinished = true
            store.global.gameVisiable = GameVisiable.HIDE
            this.needCleanSceneOnGameStart = true
        }

        JSBridgeClient.closeGame()
    }

    updataMikeUsersInfo(data: any) {
        let tempdata = data
        if (typeof data == 'string') {
            tempdata = JSON.parse(data)
        }
        console.log('平台头像信息==========', JSON.stringify(tempdata))
        store.game.paltformPlayInfo = tempdata
        cat.event.dispatchEvent(RoomEventConstant.UPDATE_PLATFORM)
    }
}
