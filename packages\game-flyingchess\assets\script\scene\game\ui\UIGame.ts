import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    _decorator,
    Component,
    error,
    EventTouch,
    instantiate,
    Label,
    Node,
    PageView,
    Prefab,
    Sprite,
    tween,
    UITransform,
    v3,
} from 'cc'
const { ccclass, property } = _decorator
import { audioEffect } from '@/core/business/hooks/Decorator'
import store from '@/core/business/store'
import {
    GameModelSinglePosMap,
    HeadNodeDefaultPosMap,
    Single,
} from '@/core/business/types/IGame'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { GameInfoBroadcast } from '@/pb-generate/server/dixit/v1/game_pb'
import {
    Player,
    PlayerInfoMessage,
    PlayerSchema,
} from '@/pb-generate/server/dixit/v1/player_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import {
    State as DeskState,
    State,
} from '@/pb-generate/server/dixit/v1/state_pb'
import { PlayerItem, PlayerState } from '../components/player/PlayerItem'
import { create } from '@bufbuild/protobuf'

import { BasePool } from '@/core/manager/gui/base/BasePool'

import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { Board } from '../components/desk/Board'

type DeskProps = {}

type DeskData = {}

declare module '@/core/manager' {
    interface Manager {
        cardPool: BasePool
    }
}

@ccclass('UIGame')
export class UIGame extends BaseComponent<DeskProps, DeskData> {
    @property({ type: Board, tooltip: '棋盘' })
    board: Board = null!

    @property({ type: Node, tooltip: '观战节点' })
    watch_node: Node = null!

    @property({ type: Node, tooltip: '玩家根节点' })
    playerRootNode: Node = null!

    @property({ type: Prefab, tooltip: '玩家预制体' })
    players_prefab: Prefab = null!

    @property({ type: Prefab, tooltip: '卡牌预制体' })
    card_item_prefab: Prefab = null!

    gamePlayers: Node[] = []

    protected override initUI(): void {
        this.board.node.active = false
        cat.cardPool = new BasePool(this.card_item_prefab)
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_GAME_INFO_BROADCAST',
                this.onBroadcastStateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_STATE_CHANGED_BROADCAST',
                this.onBroadcastStateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_INFO_MESSAGE',
                this.onPlayerInfoBroadcastHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_STATUS_CHANGED_BROADCAST',
                this.onPlayerStatusChangedHandler,
                this
            )
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.game.playerList,
            (players) => {
                players.forEach((player) => {
                    const find = this.gamePlayers.find(
                        (item) =>
                            item.getComponent(PlayerItem)?.props.player
                                .index === player.index
                    )
                    if (find) {
                        find.getComponent(PlayerItem)?.setUpdateProps({
                            player,
                        })
                    }
                })
            }
        )
    }

    /**初始化-阶段 */
    private async onPhaseInit() {
        window.ccLog('UIGame-->onPhaseInit')
        cat.audio.playEffect(AudioEffectConstant.GAME_START)
        cat.tracking.game.roomEnter()

        this.initBoard()
        // 分配座次
        await this.initPlayersDynamic()
    }

    private async onBroadcastStateSocketHandler(data: GameInfoBroadcast) {
        const { stateInfo } = data

        window.ccLog('UIGame-->onBroadcastStateSocketHandler', stateInfo)
        // 新消息的时间戳小于当前时间戳则丢弃
        if (
            data.stateInfo?.serverTimestamp &&
            store.game.roomData?.stateInfo?.serverTimestamp &&
            data.stateInfo?.serverTimestamp <
                store.game.roomData?.stateInfo?.serverTimestamp
        ) {
            window.ccLog('UIGame-->onBroadcastStateSocketHandler 丢弃旧消息')
            return
        }

        store.game.roomData = data
        // 更新socket服务器时间
        if (data.stateInfo?.serverTimestamp) {
            const diff = Number(data.stateInfo?.serverTimestamp) - Date.now()
            store.global.ws_diff_server_timer = diff
            window.ccLog('ws时间差(ms)', diff)
        }

        if (stateInfo) {
            const { state } = stateInfo
        } else {
            window.ccLog('UIGame-->stateInfo为空，丢弃旧消息')
            return
        }

        switch (data.stateInfo?.state) {
            case DeskState.GAME_START:
                this.onPhaseInit()
                break
            default:
                break
        }
    }

    private onPlayerInfoBroadcastHandler(data: PlayerInfoMessage) {
        window.ccLog('UIGame-->onPlayerInfoBroadcastHandler', data)
        cat.gui.hideLoading()
        store.game.playerInfo = data
    }

    private onPlayerStatusChangedHandler(data: Player) {
        store.game.updatePlayerStatus(data)
    }

    /**
     * 根据当前玩家位置计算显示位置索引
     * 当前玩家始终显示在左下角(位置0)，其他玩家按position顺时针排列
     */
    private getDisplayPositionIndex(playerPosition: number): number {
        const currentPlayerPosition = store.game.currentPlayerPositon
        // 计算相对位置，确保当前玩家在位置0（左下角）
        return (playerPosition - currentPlayerPosition + 4) % 4
    }

    async initPlayersDynamic() {
        window.ccLog('UIGame-->initPlayersDynamic')
        this.playerRootNode.removeAllChildren()
        this.gamePlayers = []

        const copy = [...store.game.playerList]
        if (!copy.length) return error('玩家列表不存在')

        // 先按 GameModelSinglePosMap 的位置排列玩家
        const playersPos = GameModelSinglePosMap[copy.length as Single]
        const newPlayerNodes: Node[] = []

        store.game.playerList.forEach((player, index) => {
            const player_node = instantiate(this.players_prefab)
            const { x, y } = playersPos[index]

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(x, y, 0))
                .addToParent(this.playerRootNode, {
                    props: { player },
                    data: { state: PlayerState.NORMAL },
                })

            newPlayerNodes.push(player_node)
        })

        // 等待 1 秒
        await sleep(1000)

        // 头像飞到各自的最终位置，根据当前玩家位置重新计算显示位置
        const headTweenPromises: Promise<void>[] = []

        newPlayerNodes.forEach((node, index) => {
            const player = store.game.playerList[index]
            // 计算显示位置索引，当前玩家显示在左下角
            const displayIndex = this.getDisplayPositionIndex(player.position)
            const finalPos = HeadNodeDefaultPosMap[4][displayIndex]

            headTweenPromises.push(
                new Promise((resolve) => {
                    tween(node)
                        .to(0.8, {
                            position: v3(finalPos.x, finalPos.y, 0),
                        })
                        .delay(0.2 * index)
                        .call(() => {
                            resolve()
                        })
                        .start()
                })
            )
        })

        await Promise.all(headTweenPromises)

        // 将节点添加到 gamePlayers 数组中
        this.gamePlayers = newPlayerNodes
    }

    // 游戏已经启动，直接显示玩家头像
    initPlayerStatic() {
        window.ccLog('UIGame-->initPlayerStatic')
        this.playerRootNode.removeAllChildren()
        this.gamePlayers = []

        const copy = [...store.game.playerList]
        if (!copy.length) return error('玩家列表不存在')

        // 直接把玩家头像显示在重新计算后的位置上
        store.game.playerList.forEach((player) => {
            const player_node = instantiate(this.players_prefab)
            // 计算显示位置索引，当前玩家显示在左下角
            const displayIndex = this.getDisplayPositionIndex(player.position)
            const finalPos = HeadNodeDefaultPosMap[4][displayIndex]

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(finalPos.x, finalPos.y, 0))
                .addToParent(this.playerRootNode, {
                    props: { player },
                    data: { state: PlayerState.NORMAL },
                })

            this.gamePlayers.push(player_node)
        })
    }

    // 初始化棋盘和棋子
    private initBoard() {
        this.board.node.active = true
    }

    // 在游戏开局后进入游戏,会在 Game.ts 中调用当前方法
    // 需要初始化玩家和棋盘，以及棋子的位置
    syncGameInfo() {
        window.ccLog('UIGame-->syncGameInfo')
        this.initPlayerStatic()
        this.initBoard()
    }
}
