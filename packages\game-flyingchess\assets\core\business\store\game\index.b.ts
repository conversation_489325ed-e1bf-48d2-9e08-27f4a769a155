/**
 * @describe 游戏数据
 * <AUTHOR>
 * @date 2023-08-02 20:11:20
 */

import { Node, log, math } from 'cc'

import { Player as BasePlayer, Battle, BattleSchema, Player } from 'sgc'
import { Player as GamePlayer } from '@/pb-generate/server/dixit/v1/player_pb'

import { BaseStore } from '../BaseStore'
import { Store } from '../index'
import {
    GameInfoBroadcast,
    GameInfoBroadcastSchema,
    // PlayerDrawCardMessage,
} from '@/pb-generate/server/dixit/v1/game_pb'
import { create } from '@bufbuild/protobuf'
// import { ReportGameStateRequest } from '@/pb-generate/server/shark/suileyoo/v1/report_battle_pb'
// import { PlayerItem } from '@/script/scene/game/components/player/PlayerItem'
import {
    makeAutoObservable,
    makeObservable,
    observable,
    observe,
} from 'mobx-tsuki'
import {
    PlayerAfterSelectTargetBroadcast,
    PlayerAfterSelectTargetBroadcastSchema,
    PlayerInfoMessage,
    PlayerInfoMessageSchema,
    PlayerSelectableTargetsMessage,
    PlayerSelectableTargetsMessageSchema,
} from '@/pb-generate/server/dixit/v1/player_pb'
import { PlayerItem } from '@/script/scene/game/components/player/PlayerItem'

type LayerStatusType = {
    draw: boolean
    peek: boolean
    revive: boolean
}

export type PlayerType = GamePlayer & { position: number }

export default class GameStore extends BaseStore {
    constructor(rootStore: Store) {
        super(rootStore)
        makeObservable(this, {
            roomData: observable,
            lastRoomData: observable,
            selectableTargets: observable,
            showActionButton: observable,
            selected_tooth: observable,
            is_bite: observable,
            basePlayers: observable,
            selectedCardIndex: observable,
            stealSelectedCardIndex: observable,

            playerInfo: observable,
            layerStatus: observable,
        })
        observe(this, 'roomData', ({ newValue, oldValue }) => {
            this.lastRoomData = oldValue as GameInfoBroadcast
        })
    }

    roomData: GameInfoBroadcast = create(GameInfoBroadcastSchema)

    playerInfo: PlayerInfoMessage = create(PlayerInfoMessageSchema)

    selectableTargets: PlayerSelectableTargetsMessage = create(
        PlayerSelectableTargetsMessageSchema
    )
    selectTarget: PlayerAfterSelectTargetBroadcast = create(
        PlayerAfterSelectTargetBroadcastSchema
    )

    layerStatus: LayerStatusType = {
        draw: false,
        peek: false,
        revive: false,
    }

    /**房间信息 */
    lastRoomData: GameInfoBroadcast = create(GameInfoBroadcastSchema)

    /**玩家位置信息 */
    playersPos: Map<number, math.Vec2> = new Map()

    /**结算数据 */
    // result: ReportGameStateRequest | null = null

    /**出局结算完成 */
    over_finished: boolean = false

    /**轮到玩家回合 */
    userTurn: boolean = false

    /**超时按牙数据 */
    // over_time_draw: PlayerDrawCardMessage | null | undefined = null

    /**显示动作按钮(按牙/出牌) */
    showActionButton: boolean = false

    /**允许点击牙齿 */
    is_allow_click_tooth: boolean = false

    /**玩家基础信息 */
    basePlayers: BasePlayer[] = []

    /**战局信息 */
    battle: Battle = create(BattleSchema)

    /**队伍 */
    teams: Map<string, BasePlayer[]> = new Map()

    /**选择的牙齿 */
    selected_tooth: Node[] = []

    /**鲨鱼咬合 */
    is_bite: boolean = false

    /**游戏加载完成 */
    gameLoadFinished: boolean = false

    // params 参数来自url（旧的启动游戏流程）
    isParamsFromUrl: boolean = false

    selectedCardIndex: number | undefined = undefined
    stealSelectedCardIndex: number | undefined = undefined

    //平台玩家信息(声波，头像，等级)
    paltformPlayInfo: Array<any> = []
    //获取平台用户声波
    getPlatUserWave(uid: string) {
        for (let i = 0; i < this.paltformPlayInfo.length; i++) {
            if (this.paltformPlayInfo[i].userId == Number(uid)) {
                return this.paltformPlayInfo[i].mikeSoundWave
            }
        }
        return ''
    }

    getPlatUserAvatarFrame(uid: string) {
        for (let i = 0; i < this.paltformPlayInfo.length; i++) {
            if (this.paltformPlayInfo[i].userId == Number(uid)) {
                return this.paltformPlayInfo[i].avatarFrame
            }
        }
        return ''
    }

    getPlatUserVipLevel(uid: string) {
        for (let i = 0; i < this.paltformPlayInfo.length; i++) {
            if (this.paltformPlayInfo[i].userId == Number(uid)) {
                return this.paltformPlayInfo[i].mtyMemberLevel
            }
        }
        return 0
    }

    /**根据uid获取玩家基础信息 */
    getBasePlayerByUid(uid: string) {
        return this.basePlayers.find((item) => item.id === uid)
    }

    /**根据uid获取玩家信息 */
    getPlayerByUid(uid: string) {
        return this.playerList.find((item) => item.id === uid)
    }

    /**
     * 根据index获取玩家
     * @param index 玩家索引
     */
    getPlayerComponentByIndex(players: Node, index: number) {
        const node = players.children.find((player) => {
            return player.getComponent(PlayerItem)!.props.player.index == index
        })
        return node?.getComponent(PlayerItem)
    }

    /**
     * 根据id获取玩家数据
     * @param index 玩家索引
     */
    getPlayerByIndex(index: number) {
        return this.playerList.find((player) => {
            return player.index == index
        })
    }

    /**根据team_id获取队友 */
    getTeammateByTeamId(team_id: string) {
        return this.playerList.find(
            (item) =>
                item.teamId === team_id &&
                item.index !== this.rootStore.user.userIndex
        )
    }

    /**index是用户或者用户的被观战队友(用户出局,队友存活) */
    isUserOrWatchTeammate(index: number) {
        const user = this.getPlayerByIndex(this.rootStore.user.userIndex)
        return index == user?.index
    }

    /**index是用户或者用户的队友 */
    isUserOrTeammate(index: number) {
        const player = this.getPlayerByIndex(index)
        const user = this.getPlayerByIndex(this.rootStore.user.userIndex)
        return index == user?.index || player?.teamId === user?.teamId
    }

    /**获取玩家 */
    getPlayerByKey<T extends keyof BasePlayer>(
        key: T,
        value: BasePlayer[T] | undefined
    ) {
        return this.basePlayers.find((item) => item[key] === value)
    }

    /**获取玩家用户 */
    get getUserPlayer() {
        const { user } = this.rootStore
        const nonce = user.isAudience
            ? this.getPlayerByKey('relBattleOffset', -1)
            : this.getPlayerByKey('uuid', user.auth.token)
        return nonce
    }

    get currentPlayer() {
        return this.getPlayerByIndex(this.rootStore.user.userIndex)
    }

    get currentPlayerPositon() {
        return this.currentPlayer ? this.currentPlayer.position : 0
    }

    get playerList(): PlayerType[] {
        const maxPosition = 6
        const playerLength = this.playerInfo.players.length

        const playerList = this.playerInfo.players.map((player, index) => {
            const basePlayer = this.getBasePlayerByUid(player.id)

            return {
                ...player,
                position: index,
            }
        })
        return playerList
    }

    /**玩家手牌 */
    get playerCards(): string[] {
        return this.currentPlayer?.handCards || []
    }

    //谜语人选择的故事牌
    get speakerSelectedCard(): string {
        if (this.isCurrentPlayerCursor) {
            return this.currentPlayer?.selectedCard || ''
        } else return ''
    }

    /**队伍数量 */
    teamCount: number

    /**每队人数 */
    playerInPerTeam: number

    /**房间聊天是否打开 */
    isOpenChat: boolean = false

    /**重置 */
    reset() {
        this.over_finished = false
        this.roomData = create(GameInfoBroadcastSchema)
    }

    clearSelectableTargets() {
        this.selectableTargets = create(PlayerSelectableTargetsMessageSchema)
    }

    //是否是当前玩家的回合（谜语人）
    get isCurrentPlayerCursor() {
        return (
            this.roomData.roundInfo?.currentPlayerCursor ===
            this.rootStore.user.userIndex
        )
    }

    get isBarrelZoomNear() {
        return (
            this.layerStatus.draw ||
            this.layerStatus.peek ||
            this.layerStatus.revive
        )
    }

    isSelectedCard(card: string) {
        return card === this.currentPlayer?.selectedCard
    }

    get getSpeakerPlayer() {
        return this.getPlayerByIndex(
            this.roomData.roundInfo?.currentPlayerCursor!
        )
    }

    updatePlayerStatus(player: GamePlayer) {
        // 在 playerInfo 中查找对应的玩家
        const playerIndex = this.playerInfo.players.findIndex(
            (p) => p.id === player.id
        )
        if (playerIndex === -1) return

        // 获取当前玩家信息
        const currentPlayer = this.playerInfo.players[playerIndex]

        // 定义不需要更新的字段黑名单
        const keyBlacklist = ['handCards', 'selectedCard', 'votedCard']

        // 更新除了黑名单中字段之外的所有字段
        Object.keys(player).forEach((key) => {
            if (!keyBlacklist.includes(key)) {
                // 使用类型断言确保 TypeScript 理解这是有效的操作
                ;(currentPlayer as any)[key] = (player as any)[key]
            }
        })
    }
}
